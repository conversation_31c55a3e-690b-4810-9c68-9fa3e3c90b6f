import * as vscode from "vscode";

export function activate(context: vscode.ExtensionContext) {
  context.subscriptions.push(
    vscode.commands.registerCommand("leicj.helloWorld", () => {
      const message = "Hello VS Code from leicj!";
      vscode.window.showInformationMessage(message);
    })
  );
  context.subscriptions.push(
    vscode.commands.registerCommand("VStodo.askQuestion", async () => {
      const answer = await vscode.window.showInformationMessage(
        "how are you today?",
        "happy",
        "sad"
      );

      if (answer === "happy") {
        console.log(`user is ${answer}`);
      } else {
        vscode.window.showInformationMessage("I'm sorry to hear that");
      }
    })
  );
}

export function deactivate() {}
