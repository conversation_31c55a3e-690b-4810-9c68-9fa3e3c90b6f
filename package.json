{"name": "<PERSON><PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON>", "description": "my first extension", "version": "0.0.1", "engines": {"vscode": "^1.54.0"}, "categories": ["Other"], "activationEvents": [], "main": "./dist/extension.js", "contributes": {"commands": [{"command": "leicj.helloWorld", "title": "Hello World"}, {"command": "VStodo.askQuestion", "category": "VStodo", "title": "askQuestion"}]}, "scripts": {"vscode:prepublish": "npm run package", "compile": "webpack", "watch": "webpack --watch", "package": "webpack --mode production --devtool hidden-source-map", "compile-tests": "tsc -p . --outDir out", "watch-tests": "tsc -p . -w --outDir out", "pretest": "npm run compile-tests && npm run compile && npm run lint", "lint": "eslint src", "test": "vscode-test"}, "devDependencies": {"@types/vscode": "^1.54.0", "@types/mocha": "^10.0.10", "@types/node": "20.x", "@typescript-eslint/eslint-plugin": "^8.31.1", "@typescript-eslint/parser": "^8.31.1", "eslint": "^9.25.1", "typescript": "^5.8.3", "ts-loader": "^9.5.2", "webpack": "^5.99.7", "webpack-cli": "^6.0.1", "@vscode/test-cli": "^0.0.11", "@vscode/test-electron": "^2.5.2"}}