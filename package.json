{"name": "<PERSON><PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON>", "description": "my first extension", "version": "0.0.1", "engines": {"vscode": "^1.54.0"}, "categories": ["Other"], "activationEvents": [], "main": "./dist/extension.js", "contributes": {"commands": [{"command": "leicj.helloWorld", "title": "Hello World"}, {"command": "VStodo.askQuestion", "category": "VStodo", "title": "askQuestion"}, {"command": "VStodo.refresh", "category": "VStodo", "title": "Refresh"}], "viewsContainers": {"activitybar": [{"id": "vstode-sidebar-view", "title": "VSTodo3", "icon": "media/heart.svg"}]}, "views": {"vstode-sidebar-view": [{"type": "webview", "id": "vstode-sidebar", "name": "VSTodo", "icon": "media/heart.svg", "contextualTitle": "VSTodo"}]}}, "scripts": {"vscode:prepublish": "npm run package", "compile": "webpack", "watch": "concurrently \"rollup -c -w\" webpack --watch --config webpack.config.js", "package": "webpack --mode production --devtool hidden-source-map", "compile-tests": "tsc -p . --outDir out", "watch-tests": "tsc -p . -w --outDir out", "pretest": "npm run compile-tests && npm run compile && npm run lint", "lint": "eslint src", "test": "vscode-test"}, "devDependencies": {"@rollup/plugin-commonjs": "^28.0.6", "@rollup/plugin-node-resolve": "^16.0.1", "@rollup/plugin-typescript": "^12.1.4", "@tsconfig/svelte": "^5.0.4", "@types/mocha": "^10.0.10", "@types/node": "20.x", "@types/vscode": "^1.54.0", "@typescript-eslint/eslint-plugin": "^8.31.1", "@typescript-eslint/parser": "^8.31.1", "@vscode/test-cli": "^0.0.11", "@vscode/test-electron": "^2.5.2", "concurrently": "^9.2.0", "eslint": "^9.25.1", "rollup-plugin-css-only": "^4.5.2", "rollup-plugin-svelte": "^7.2.2", "rollup-plugin-terser": "^7.0.2", "svelte": "^5.37.3", "svelte-check": "^4.3.1", "svelte-preprocess": "^6.0.3", "ts-loader": "^9.5.2", "typescript": "^5.8.3", "webpack": "^5.99.7", "webpack-cli": "^6.0.1"}}